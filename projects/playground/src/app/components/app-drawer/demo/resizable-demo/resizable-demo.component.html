<div class="resizable-demo">
  <div class="demo-header">
    <h3>Resizable Drawer</h3>
    <p>Enable resizing functionality for adjustable drawer dimensions.</p>
  </div>

  <div class="demo-content">
    <div class="button-group">
      <ava-button
        label="Basic Resizable"
        (click)="openDrawer('resizable')"
        variant="secondary"
      >
      </ava-button>
      <ava-button
        label="Custom Size"
        (click)="openDrawer('custom-size')"
        variant="secondary"
      >
      </ava-button>
      <ava-button
        label="Constrained"
        (click)="openDrawer('constrained')"
        variant="secondary"
      >
      </ava-button>
      <ava-button
        label="Multi Position"
        (click)="openDrawer('multi-position')"
        variant="secondary"
      >
      </ava-button>
    </div>

    <!-- Basic Resizable -->
    <ava-drawer
      [isOpen]="resizableDrawerOpen"
      [resizable]="true"
      title="Resizable Drawer"
      subtitle="Drag the edge to resize"
      (closed)="closeDrawer('resizable')"
    >
      <div class="content-section">
        <h4>Basic Resizable Drawer</h4>
        <p>This drawer has <code>[resizable]="true"</code> enabled.</p>
        <p>
          Try dragging the left edge of this drawer to resize it. The drawer
          will maintain its new size until closed.
        </p>

        <div class="resize-info">
          <h5>Resize Features:</h5>
          <ul>
            <li>✅ Drag the edge to resize</li>
            <li>✅ Maintains size until closed</li>
            <li>✅ Smooth resize animation</li>
            <li>✅ Works with all positions</li>
          </ul>
        </div>

        <div class="content-block">
          <h5>Content Adapts</h5>
          <p>
            The content inside the drawer automatically adapts to the new size.
            Text will reflow and components will adjust their layout
            accordingly.
          </p>
          <p>This makes resizable drawers perfect for:</p>
          <ul>
            <li>Code editors with adjustable panels</li>
            <li>Design tools with flexible workspaces</li>
            <li>Documentation with customizable reading areas</li>
            <li>
              Any interface where users need control over space allocation
            </li>
          </ul>
        </div>
      </div>
    </ava-drawer>

    <!-- Custom Size Resizable -->
    <ava-drawer
      [isOpen]="customSizeDrawerOpen"
      [resizable]="true"
      width="600px"
      title="Custom Size Resizable"
      subtitle="Starts at 600px, can be resized"
      (closed)="closeDrawer('custom-size')"
    >
      <div class="content-section">
        <h4>Custom Initial Size</h4>
        <p>
          This drawer starts with a custom width of 600px and can be resized
          from there.
        </p>
        <p>
          Use the <code>width</code> property to set the initial size when using
          <code>[resizable]="true"</code>.
        </p>

        <div class="size-info">
          <h5>Size Configuration:</h5>
          <ul>
            <li>Initial width: 600px</li>
            <li>Resizable: Yes</li>
            <li>Minimum width: 320px (default)</li>
            <li>Maximum width: 90% viewport</li>
          </ul>
        </div>

        <div class="content-block">
          <h5>Perfect for Complex Interfaces</h5>
          <p>
            Custom initial sizes are great when you know the optimal starting
            size for your content but still want to give users flexibility.
          </p>
          <p>Common use cases:</p>
          <ul>
            <li>Form editors with known field widths</li>
            <li>Data tables with specific column requirements</li>
            <li>Code editors with syntax highlighting</li>
            <li>Design tools with specific tool panels</li>
          </ul>
        </div>
      </div>
    </ava-drawer>

    <!-- Constrained Resizable -->
    <ava-drawer
      [isOpen]="constrainedDrawerOpen"
      [resizable]="true"
      width="400px"
      maxWidth="800px"
      minWidth="300px"
      title="Constrained Resizable"
      subtitle="Limited resize range: 300px - 800px"
      (closed)="closeDrawer('constrained')"
    >
      <div class="content-section">
        <h4>Constrained Resize Range</h4>
        <p>This drawer has both minimum and maximum width constraints.</p>
        <p>
          Use <code>minWidth</code> and <code>maxWidth</code> to control the
          resize range.
        </p>

        <div class="constraint-info">
          <h5>Resize Constraints:</h5>
          <ul>
            <li>Minimum width: 300px</li>
            <li>Maximum width: 800px</li>
            <li>Initial width: 400px</li>
            <li>Resize range: 500px total</li>
          </ul>
        </div>

        <div class="content-block">
          <h5>Why Use Constraints?</h5>
          <p>
            Constraints ensure your drawer always maintains usable dimensions:
          </p>
          <ul>
            <li>Prevents drawers from becoming too small to use</li>
            <li>Stops drawers from taking up too much screen space</li>
            <li>Maintains consistent user experience</li>
            <li>Ensures content remains readable and accessible</li>
          </ul>
        </div>

        <div class="content-block">
          <h5>Try Resizing</h5>
          <p>
            Drag the edge to resize this drawer. Notice how it stops at the
            minimum (300px) and maximum (800px) boundaries.
          </p>
          <p>
            The resize handle will become less responsive when you reach the
            limits, providing visual feedback about the constraints.
          </p>
        </div>
      </div>
    </ava-drawer>

    <!-- Multi Position Resizable -->
    <ava-drawer
      [isOpen]="multiPositionDrawerOpen"
      [resizable]="true"
      position="left"
      width="350px"
      title="Left Position Resizable"
      subtitle="Resizable from the right edge"
      (closed)="closeDrawer('multi-position')"
    >
      <div class="content-section">
        <h4>Multi-Position Resizable</h4>
        <p>
          Resizable drawers work with all positions: left, right, top, and
          bottom.
        </p>
        <p>
          For left-positioned drawers, the resize handle appears on the right
          edge.
        </p>

        <div class="position-info">
          <h5>Position-Specific Behavior:</h5>
          <ul>
            <li>Left position: Resize from right edge</li>
            <li>Right position: Resize from left edge</li>
            <li>Top position: Resize from bottom edge</li>
            <li>Bottom position: Resize from top edge</li>
          </ul>
        </div>

        <div class="content-block">
          <h5>Navigation Drawers</h5>
          <p>
            Left-positioned resizable drawers are perfect for navigation panels
            where users might want to:
          </p>
          <ul>
            <li>Expand to see more menu items</li>
            <li>Shrink to maximize content area</li>
            <li>Adjust based on content length</li>
            <li>Customize their workspace layout</li>
          </ul>
        </div>

        <div class="content-block">
          <h5>Responsive Design</h5>
          <p>
            Resizable drawers automatically adapt to different screen sizes:
          </p>
          <ul>
            <li>Mobile: Limited resize range</li>
            <li>Tablet: Moderate resize range</li>
            <li>Desktop: Full resize range</li>
            <li>Large screens: Extended resize capabilities</li>
          </ul>
        </div>
      </div>
    </ava-drawer>
  </div>
</div>
