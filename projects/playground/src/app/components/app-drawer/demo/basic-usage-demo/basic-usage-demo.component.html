<div class="basic-usage-demo">
  <div class="demo-content">
    <ava-button label="Open Drawer" variant="primary" (click)="openDrawer()">
    </ava-button>

    <ava-drawer
      [isOpen]="isDrawerOpen"
      title="Basic Drawer"
      subtitle="This is a simple drawer example"
      (opened)="onDrawerOpened()"
      (closed)="onDrawerClosed()"
      (closed)="closeDrawer()"
    >
      <div class="drawer-content">
        <h4>Welcome to the Drawer!</h4>
        <p>This is a basic drawer component with default settings:</p>
        <ul>
          <li>Position: Right (default)</li>
          <li>Size: Medium (default)</li>
          <li>Overlay: Enabled</li>
          <li>Animations: Enabled</li>
        </ul>
        <p>You can close this drawer by:</p>
        <ul>
          <li>Clicking the X button</li>
          <li>Clicking the overlay</li>
          <li>Pressing the Escape key</li>
        </ul>
      </div>

      <div slot="footer">
        <ava-button label="Close" variant="secondary" (click)="closeDrawer()">
        </ava-button>
        <ava-button label="Save" variant="primary" (click)="closeDrawer()">
        </ava-button>
      </div>
    </ava-drawer>
  </div>
</div>
