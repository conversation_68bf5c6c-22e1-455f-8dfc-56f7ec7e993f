<div class="sizes-demo">
  <div class="demo-content">
    <div class="button-group">
      <ava-button
        label="Small"
        (click)="openDrawer('small')"
        variant="secondary"
      >
      </ava-button>
      <ava-button
        label="Medium"
        (click)="openDrawer('medium')"
        variant="secondary"
      >
      </ava-button>
      <ava-button
        label="Large"
        (click)="openDrawer('large')"
        variant="secondary"
      >
      </ava-button>
      <ava-button
        label="Extra Large"
        (click)="openDrawer('extra-large')"
        variant="secondary"
      >
      </ava-button>
      <ava-button
        label="Full Size"
        (click)="openDrawer('full')"
        variant="secondary"
      >
      </ava-button>
    </div>

    <!-- Small Size -->
    <ava-drawer
      [isOpen]="smallDrawerOpen"
      size="small"
      title="Small Drawer"
      (closed)="closeDrawer('small')"
    >
      <div class="drawer-content">
        <h4>Small Size (320px)</h4>
        <p>This is a small drawer perfect for simple forms or quick actions.</p>
        <ul>
          <li>Width: 320px</li>
          <li>Height: 200px (for top/bottom)</li>
          <li>Perfect for simple forms</li>
          <li>Quick actions and notifications</li>
        </ul>
      </div>
    </ava-drawer>

    <!-- Medium Size (Default) -->
    <ava-drawer
      [isOpen]="mediumDrawerOpen"
      size="medium"
      title="Medium Drawer"
      (closed)="closeDrawer('medium')"
    >
      <div class="drawer-content">
        <h4>Medium Size (480px)</h4>
        <p>
          This is a medium drawer - the default size, good for most use cases.
        </p>
        <ul>
          <li>Width: 480px</li>
          <li>Height: 300px (for top/bottom)</li>
          <li>Default size</li>
          <li>Good for most use cases</li>
        </ul>
      </div>
    </ava-drawer>

    <!-- Large Size -->
    <ava-drawer
      [isOpen]="largeDrawerOpen"
      size="large"
      title="Large Drawer"
      (closed)="closeDrawer('large')"
    >
      <div class="drawer-content">
        <h4>Large Size (640px)</h4>
        <p>This is a large drawer great for detailed forms or content.</p>
        <ul>
          <li>Width: 640px</li>
          <li>Height: 400px (for top/bottom)</li>
          <li>Great for detailed forms</li>
          <li>Content-heavy interfaces</li>
        </ul>
      </div>
    </ava-drawer>

    <!-- Extra Large Size -->
    <ava-drawer
      [isOpen]="extraLargeDrawerOpen"
      size="extra-large"
      title="Extra Large Drawer"
      (closed)="closeDrawer('extra-large')"
    >
      <div class="drawer-content">
        <h4>Extra Large Size (800px)</h4>
        <p>
          This is an extra large drawer for complex interfaces or detailed
          views.
        </p>
        <ul>
          <li>Width: 800px</li>
          <li>Height: 500px (for top/bottom)</li>
          <li>Complex interfaces</li>
          <li>Detailed views and dashboards</li>
        </ul>
      </div>
    </ava-drawer>

    <!-- Full Size -->
    <ava-drawer
      [isOpen]="fullDrawerOpen"
      size="full"
      title="Full Size Drawer"
      (closed)="closeDrawer('full')"
    >
      <div class="drawer-content">
        <h4>Full Size (100% viewport)</h4>
        <p>
          This drawer takes up the full viewport - essentially a full-screen
          modal experience.
        </p>
        <ul>
          <li>Width: 100% viewport</li>
          <li>Height: 100% viewport</li>
          <li>Full-screen modal experience</li>
          <li>Immersive content display</li>
        </ul>
      </div>
    </ava-drawer>
  </div>
</div>
